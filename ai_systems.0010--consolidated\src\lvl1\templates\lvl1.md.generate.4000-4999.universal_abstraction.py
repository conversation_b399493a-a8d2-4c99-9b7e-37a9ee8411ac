import os

OUTPUT_DIR = "src/lvl1/templates/md"

TEMPLATES = {

    # 4000-4999: Universal Abstraction Engine
    # ---
    "4000-a-domain_neutralizer": {
        "title": "Domain Neutralizer",
        "interpretation": "Your goal is not to **preserve** domain-specific terminology, but to **neutralize** all specialized language into universal, archetypal constructs. Execute as:",
        "transformation": "`{role=domain_abstraction_engine; input=[prompt:str]; process=[identify_domain_specific_terms(), map_to_universal_archetypes(), replace_technical_jargon_with_conceptual_frameworks(), eliminate_industry_specific_references(), preserve_core_operational_intent()]; constraints=[maintain_functional_equivalence(), ensure_cross_domain_applicability(), preserve_logical_structure()]; requirements=[domain_agnostic_language(), universal_terminology(), archetypal_constructs()]; output={neutralized_prompt:str}}`",
    },
    "4000-b-structural_abstractor": {
        "title": "Structural Abstractor", 
        "interpretation": "Your goal is not to **modify** the prompt's structure, but to **abstract** its organizational patterns into universal frameworks. Execute as:",
        "transformation": "`{role=structural_pattern_abstractor; input=[neutralized_prompt:str]; process=[identify_organizational_patterns(), extract_logical_flow_structures(), map_to_universal_frameworks(), generalize_sequential_dependencies(), abstract_hierarchical_relationships()]; constraints=[preserve_logical_coherence(), maintain_operational_sequence(), ensure_pattern_transferability()]; requirements=[universal_structural_patterns(), framework_agnostic_organization(), archetypal_flow_logic()]; output={abstracted_structure:str}}`",
    },
    "4000-c-purpose_distiller": {
        "title": "Purpose Distiller",
        "interpretation": "Your goal is not to **explain** the prompt's intent, but to **distill** its fundamental purpose into universal transformation patterns. Execute as:",
        "transformation": "`{role=purpose_essence_extractor; input=[abstracted_structure:str]; process=[isolate_core_transformation_intent(), identify_universal_change_patterns(), extract_archetypal_objectives(), map_to_fundamental_operations(), synthesize_purpose_essence()]; constraints=[eliminate_context_dependencies(), focus_on_transformation_mechanics(), preserve_outcome_directionality()]; requirements=[universal_purpose_statement(), archetypal_transformation_pattern(), context_independent_objectives()]; output={distilled_purpose:str}}`",
    },
    "4000-d-analogy_mapper": {
        "title": "Analogy Mapper",
        "interpretation": "Your goal is not to **retain** specific examples, but to **map** all concrete references to high-level, transferable analogies. Execute as:",
        "transformation": "`{role=universal_analogy_generator; input=[distilled_purpose:str]; process=[identify_concrete_references(), extract_underlying_patterns(), generate_archetypal_analogies(), create_transferable_metaphors(), establish_universal_conceptual_bridges()]; constraints=[ensure_analogy_universality(), maintain_conceptual_accuracy(), preserve_relational_dynamics()]; requirements=[domain_agnostic_analogies(), archetypal_metaphors(), universal_conceptual_frameworks()]; output={analogized_framework:str}}`",
    },
    "4000-e-template_synthesizer": {
        "title": "Template Synthesizer",
        "interpretation": "Your goal is not to **finalize** the abstraction, but to **synthesize** all elements into a universally applicable template engine. Execute as:",
        "transformation": "`{role=template_synthesis_operator; input=[analogized_framework:str]; process=[synthesize_template_structure(), identify_modification_vectors(), create_controlled_variation_points(), establish_reusability_patterns(), generate_universal_instruction_format()]; constraints=[ensure_template_completeness(), maintain_modification_precision(), preserve_universal_applicability()]; requirements=[systematic_customization_points(), archetypal_instruction_format(), maximal_transferability()]; output={universal_template:str}}`",
    },
}

def create_template_files():
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    created_files = []

    for filename, template in TEMPLATES.items():
        filepath = os.path.join(OUTPUT_DIR, f"{filename}.md")
        content = f"[{template['title']}] {template['interpretation']} {template['transformation']}"

        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)

        created_files.append(f"{filename}.md")

    return created_files

def main():
    created_files = create_template_files()

    print("Successfully created Universal Abstraction Engine templates:")
    for file in created_files:
        print(f"  - {file}")
    
    print("\n=== UNIVERSAL ABSTRACTION ENGINE ===")
    print("Sequence 4000: Transform any input into archetypal, domain-agnostic form")
    print("\nSteps:")
    print("  a. Domain Neutralizer - Replace specialized terms with universal archetypes")
    print("  b. Structural Abstractor - Abstract organizational patterns into frameworks") 
    print("  c. Purpose Distiller - Extract fundamental transformation essence")
    print("  d. Analogy Mapper - Convert concrete references to transferable analogies")
    print("  e. Template Synthesizer - Create universally applicable template engine")
    
    print("\n=== USAGE EXAMPLES ===")
    print("Complete sequence: --sequence 4000")
    print("Partial sequence: --sequence 4000:a-c")
    print("Single step: --sequence 4000-d-analogy_mapper")
    print("Chain mode: --sequence 4000 --chain-mode")

if __name__ == "__main__":
    main()
